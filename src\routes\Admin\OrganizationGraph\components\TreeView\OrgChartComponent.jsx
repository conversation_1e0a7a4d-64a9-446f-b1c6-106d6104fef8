import React, { useEffect, useLayoutEffect, useRef, useState } from 'react';
import { Menu, MenuItem, SvgIcon, Typography } from '@mui/material';
import { OrgChart } from 'd3-org-chart';
import GroupsIcon from '@mui/icons-material/Groups';
import AddIcon from '@mui/icons-material/Add';
import EditIcon from '@mui/icons-material/Edit';

import DeleteIcon from '@mui/icons-material/Delete';
import { ReactComponent as BellSimpleIcon } from 'static/icons/BellSimple.svg';
import { ReactComponent as ChatTextIcon } from 'static/icons/ChatText.svg';
import ChartControls from './ChartControls';

function OrgChartComponent({ data, handleMenuItemClick }) {
  const d3Container = useRef(null);
  const chartRef = useRef(new OrgChart());

  const [contextMenu, setContextMenu] = useState(null);
  const [selectedNode, setSelectedNode] = useState(null);
  const doItOnce = useRef(false);

  useLayoutEffect(() => {
    if (data && data.length > 0 && d3Container.current && !doItOnce.current) {
      doItOnce.current = true;
      chartRef.current
        .container(d3Container.current)
        .data(data)
        .nodeWidth(() => 200)
        .nodeHeight(() => 100)
        .childrenMargin(d => 50)
        .compactMarginBetween(d => 45)
        .compactMarginPair(d => 30)
        .neighbourMargin((a, b) => 40)
        .svgHeight(700)
        .nodeContent((d, i, arr, state) => {
          const color = '#4eb3a5';
          const hoverColor = 'rgb(230, 81, 0)'; // Hover background color

          const { data } = d;
          return `
        <div
          style="
            width:${d.width}px;
            height:${d.height}px;
            padding-top:-25px;
            padding-left:1px;
            padding-right:1px;
            font-family: IRANSans;"
        >
          <div
            style="
              font-family: 'IRANSansX', sans-serif;
              background-color:${data._highlighted ? hoverColor : color};
              margin-left:-1px;
              width:${d.width - 2}px;
              height:${d.height}px;
              border-radius:10px;
              border: 1px solid #E4E2E9;
              box-shadow: rgba(0, 0, 0, 0.2) 0px 3px 1px -2px,
                          rgba(0, 0, 0, 0.14) 0px 2px 2px 0px,
                          rgba(0, 0, 0, 0.12) 0px 1px 5px 0px;"
            onmouseover="this.style.backgroundColor='${hoverColor}'"
            onmouseout="this.style.backgroundColor='${data._highlighted ? hoverColor : color}'"
            oncontextmenu="window.openTreeViewContextMenu(event, ${data.id}, '${data.name}', '${data.avatar}')"
            >

            <div
              style="
                background-color:#fff;
                margin:0 auto;
                margin-top:-27px;
                border-radius:100px;
                width:50px;
                height:50px;
                border: 1px solid #E4E2E9">
            </div>
            <div
              style="
                margin-top:-50px;
                text-align: center;">
              <img
                src="${d.data.avatar || '/logo.png'}"
                style="
                  margin: 0 auto;
                  border-radius:100px;
                  width:50px;
                  height:50px;" />
            </div>
            <div
              style="
                font-size:15px;
                color:#fff;
                text-align: center;
                margin-top:10px">
              ${d.data.name}
            </div>
          </div>
        </div>`;
        })
        .compact(true)
        .onExpandOrCollapse(a => {
          if (a.depth > 0) chartRef.current.fit();
        })
        .defaultFont('IRANSans')
        .layout('top')
        .render();

      window.openTreeViewContextMenu = (e, id, name, avatar) => {
        setSelectedNode({ id, name, avatar });
        setContextMenu(
          contextMenu === null
            ? {
                mouseX: e.clientX + 2,
                mouseY: e.clientY - 6,
              }
            : null,
        );
      };
    }
  }, [data, d3Container.current]);

  const handleClose = () => {
    setContextMenu(null);
    setSelectedNode(null);
  };

  return (
    <div>
      <ChartControls
        chartRef={chartRef}
        organizationName={data?.[0]?.name || ''}
      />
      <Typography sx={{ width: '100%', fontSize: '14px', pt: '5px' }}>
        ** جهت دسترسی به منوی ابزار بر روی سازمان مورد نظر{' '}
        <span style={{ fontWeight: 'bold' }}>کلیک راست</span> کنید **
      </Typography>
      <div
        ref={d3Container}
        id="chart-container"
        style={{
          background: '#fff',
          border: '2px solid #1cbcb4',
          borderRadius: '10px',
          marginTop: 10,
          color: 'white',
        }}
      />

      <Menu
        open={contextMenu !== null}
        onClose={handleClose}
        anchorReference="anchorPosition"
        anchorPosition={
          contextMenu !== null
            ? { top: contextMenu.mouseY, left: contextMenu.mouseX }
            : undefined
        }
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'center',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'center',
        }}
      >
        <MenuItem
          onClick={() => handleMenuItemClick('showUsers', selectedNode)}
          sx={{ fontSize: 14 }}
        >
          <GroupsIcon style={{ fontSize: 15, marginLeft: 4, color: '#888' }} />
          مدیریت اعضا
        </MenuItem>
        <MenuItem
          onClick={() =>
            handleMenuItemClick('addOrganizationChart', selectedNode)
          }
          sx={{ fontSize: 14 }}
        >
          <AddIcon style={{ fontSize: 15, marginLeft: 4, color: '#888' }} />
          افزودن زیرسازمان
        </MenuItem>
        <MenuItem
          onClick={() => handleMenuItemClick('sendAnnounce', selectedNode)}
          sx={{ fontSize: 14 }}
        >
          <SvgIcon
            component={BellSimpleIcon}
            style={{ fontSize: 15, marginLeft: 4 }}
          />
          ارسال اعلان
        </MenuItem>
        <MenuItem
          onClick={() => handleMenuItemClick('sendMessage', selectedNode)}
          sx={{ fontSize: 14 }}
        >
          <SvgIcon
            component={ChatTextIcon}
            style={{ fontSize: 15, marginLeft: 4 }}
          />
          {/* <ChatTextIcon style={{ fontSize: 15, marginLeft: 4 }} /> */}
          ارسال پیام گروهی
        </MenuItem>
        <MenuItem
          onClick={() => handleMenuItemClick('edit', selectedNode)}
          sx={{ fontSize: 14 }}
        >
          <EditIcon style={{ fontSize: 15, marginLeft: 4, color: '#888' }} />
          ویرایش
        </MenuItem>
        <MenuItem
          onClick={() => {
            handleMenuItemClick('delete', selectedNode);
            handleClose();
          }}
          sx={{ fontSize: 14 }}
        >
          <DeleteIcon style={{ fontSize: 15, marginLeft: 4, color: '#888' }} />
          حذف
        </MenuItem>
      </Menu>
    </div>
  );
}

export default OrgChartComponent;
