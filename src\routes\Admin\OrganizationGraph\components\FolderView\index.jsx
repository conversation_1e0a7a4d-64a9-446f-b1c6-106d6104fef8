import React, { useState, useEffect } from 'react';
import {
  Avatar,
  Menu,
  MenuItem,
  Box,
  Typography,
  SvgIcon,
} from '@mui/material';
import ChevronRightIcon from '@mui/icons-material/ChevronRight';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import { SimpleTreeView } from '@mui/x-tree-view/SimpleTreeView';
import { TreeItem, treeItemClasses } from '@mui/x-tree-view/TreeItem';
import GroupsIcon from '@mui/icons-material/Groups';
import AddIcon from '@mui/icons-material/Add';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import IndeterminateCheckBoxRoundedIcon from '@mui/icons-material/IndeterminateCheckBoxRounded';
import DisabledByDefaultRoundedIcon from '@mui/icons-material/DisabledByDefaultRounded';
import AddBoxRoundedIcon from '@mui/icons-material/AddBoxRounded';
import { ReactComponent as BellSimpleIcon } from 'static/icons/BellSimple.svg';
import { ReactComponent as ChatTextIcon } from 'static/icons/ChatText.svg';
import { styled, alpha } from '@mui/system';

// Custom label component to display an avatar and text
const TreeNodeLabel = ({ label, imageUrl }) => (
  <Box display="flex" alignItems="center">
    <Avatar
      className="tree-avatar-item"
      src={imageUrl}
      sx={{ width: 24, height: 24, mr: 1 }}
    />
    <Typography variant="body2">{label}</Typography>
  </Box>
);

// Helper function to transform flat data to tree structure
const buildTree = data => {
  const map = {};
  const tree = [];

  data.forEach(item => {
    map[item.id] = { ...item, children: [] }; // Map each item by id with an empty children array
  });

  data.forEach(item => {
    if (item.parent === null) {
      tree.push(map[item.id]); // Root node has no parent
    } else {
      map[item.parent]?.children.push(map[item.id]); // Add item as a child of its parent
    }
  });

  return tree;
};

const CustomTreeItem = styled(TreeItem)(({ theme }) => ({
  [`& .${treeItemClasses.content}`]: {
    padding: theme.spacing(0.5, 1),
    margin: theme.spacing(0.2, 0),
  },
  [`& .${treeItemClasses.iconContainer}`]: {
    '& .close': {
      opacity: 0.3,
    },
  },
  [`& .${treeItemClasses.groupTransition}`]: {
    marginLeft: 15,
    paddingLeft: 18,
    // borderLeft: `1px dashed ${alpha(theme.palette.text.primary, 0.4)}`,
  },
  [`& .${treeItemClasses.expanded}`]: {
    color: '#11a6a1',
  },
  [`& .${treeItemClasses.expanded} .tree-avatar-item`]: {
    border: '2px solid #11a6a1',
  },
}));

function ExpandIcon(props) {
  return <AddBoxRoundedIcon {...props} sx={{ opacity: 0.8 }} />;
}

function CollapseIcon(props) {
  return <IndeterminateCheckBoxRoundedIcon {...props} sx={{ opacity: 0.8 }} />;
}

function EndIcon(props) {
  // return <DisabledByDefaultRoundedIcon {...props} sx={{ opacity: 0.3 }} />;
  return null;
}

function FolderView({ data, handleMenuItemClick }) {
  const [treeData, setTreeData] = useState([]);
  const [expandedItems, setExpandedItems] = useState([]);
  const [contextMenu, setContextMenu] = useState(null);
  const [selectedNode, setSelectedNode] = useState(null);

  // Fetch tree data from the API on mount
  useEffect(() => {
    const builtData = buildTree(data);
    const expandedKeys = [
      ...builtData.map(item => item.id.toString()), // First level
      ...builtData.flatMap(item =>
        item.children?.map(child => child.id.toString()),
      ), // Second level
    ];

    setExpandedItems(expandedKeys);
    setTreeData(builtData);
  }, [data]);

  // Right-click handler for showing context menu
  const handleRightClick = (event, node) => {
    event.preventDefault();
    event.stopPropagation();
    setSelectedNode(node);
    setContextMenu(
      contextMenu === null
        ? {
            mouseX: event.clientX + 2,
            mouseY: event.clientY - 6,
          }
        : null,
    );
  };

  const handleNodeToggle = (event, nodeIds) => {
    setExpandedItems(nodeIds);
  };

  // Close context menu
  const handleClose = () => {
    setContextMenu(null);
    setSelectedNode(null);
  };

  const renderTree = nodes =>
    nodes.map(node => (
      <CustomTreeItem
        key={node.id}
        itemId={node.id.toString()}
        label={<TreeNodeLabel label={node.name} imageUrl={node.avatar} />}
        onContextMenu={event => handleRightClick(event, node)}
      >
        {Array.isArray(node.children) ? renderTree(node.children) : null}
      </CustomTreeItem>
    ));

  return (
    <Box
      sx={{
        mb: 3,
        borderRadius: '8px',
        background: 'white',
        boxShadow: '0px 2px 20px 0px #00000012',
        width: '100%',
        height: '100%',
        overflow: 'auto',
        // maxHeight: '70vh',
        minHeight: '50vh',
        p: 2,
      }}
    >
      <Box sx={{ width: '100%', direction: 'ltr' }}>
        <Typography sx={{ width: '100%', fontSize: '14px', py: '5px' }}>
          ** جهت دسترسی به منوی ابزار بر روی سازمان مورد نظر{' '}
          <span style={{ fontWeight: 'bold' }}>کلیک راست</span> کنید **
        </Typography>
        <SimpleTreeView
          aria-label="dynamic tree"
          expandedItems={expandedItems}
          onExpandedItemsChange={handleNodeToggle}
          slots={{
            expandIcon: ExpandIcon,
            collapseIcon: CollapseIcon,
            endIcon: EndIcon,
          }}
          sx={{ flexGrow: 1, maxWidth: '50%', overflowY: 'auto' }}
        >
          {renderTree(treeData)}
        </SimpleTreeView>

        <Menu
          open={contextMenu !== null}
          onClose={handleClose}
          anchorReference="anchorPosition"
          anchorPosition={
            contextMenu !== null
              ? { top: contextMenu.mouseY, left: contextMenu.mouseX }
              : undefined
          }
          anchorOrigin={{
            vertical: 'bottom',
            horizontal: 'center',
          }}
          transformOrigin={{
            vertical: 'top',
            horizontal: 'center',
          }}
        >
          <MenuItem
            onClick={() => handleMenuItemClick('showUsers', selectedNode)}
            sx={{ fontSize: 14 }}
          >
            <GroupsIcon
              style={{ fontSize: 15, marginLeft: 4, color: '#888' }}
            />
            مدیریت اعضا
          </MenuItem>
          <MenuItem
            onClick={() =>
              handleMenuItemClick('addOrganizationChart', selectedNode)
            }
            sx={{ fontSize: 14 }}
          >
            <AddIcon style={{ fontSize: 15, marginLeft: 4, color: '#888' }} />
            افزودن زیرسازمان
          </MenuItem>
          <MenuItem
            onClick={() => handleMenuItemClick('sendAnnounce', selectedNode)}
            sx={{ fontSize: 14 }}
          >
            <SvgIcon
              component={BellSimpleIcon}
              style={{ fontSize: 15, marginLeft: 4 }}
            />
            ارسال اعلان
          </MenuItem>
          <MenuItem
            onClick={() => handleMenuItemClick('sendMessage', selectedNode)}
            sx={{ fontSize: 14 }}
          >
            <SvgIcon
              component={ChatTextIcon}
              style={{ fontSize: 15, marginLeft: 4 }}
            />
            {/* <ChatTextIcon style={{ fontSize: 15, marginLeft: 4 }} /> */}
            ارسال پیام گروهی
          </MenuItem>
          <MenuItem
            onClick={() => handleMenuItemClick('edit', selectedNode)}
            sx={{ fontSize: 14 }}
          >
            <EditIcon style={{ fontSize: 15, marginLeft: 4, color: '#888' }} />
            ویرایش
          </MenuItem>
          <MenuItem
            onClick={() => {
              handleMenuItemClick('delete', selectedNode);
              handleClose();
            }}
            sx={{ fontSize: 14 }}
          >
            <DeleteIcon
              style={{ fontSize: 15, marginLeft: 4, color: '#888' }}
            />
            حذف
          </MenuItem>
        </Menu>
      </Box>
    </Box>
  );
}

export default FolderView;
