import { createBrowserRouter } from 'react-router-dom';
import Layout from '../components/Layout/Layout';
import AdminLayout from '../components/AdminLayout/AdminLayout';
import Content, { loader as contentLoader } from './Content/Content';
import EnterMobile from './EnterMobile/EnterMobile';
import Home from './Home/Home';
import Profile, { loader as profileLoader } from './Profile/Profile';
import RegisterationFrom from './RegistrationForm/RegisterationForm';
import Search, { loader as searchLoader } from './Search/Search';
import ValidateOTP from './ValidateOTP/ValidateOTP';
import { PATHS } from '../constants';
import AuthRequired from '../components/AuthRequired/AuthRequired';
import UpdateContent, {
  loader as contentUpdateLoader,
} from './UpdateContent/UpdateContent';
import CreateContent from './CreateContent/CreateContent';
import Settings from './Settings/Settings';
import ContentAnalytics, {
  loader as contentAnalyticsLoader,
} from './ContentAnalytics/ContentAnalytics';
import ContentReels, {
  loader as contentReelsLoader,
} from './ContentReels/ContentReels';
import ContentEvaluate, {
  loader as contentEvaluateLoader,
} from './ContentEvaluate/ContentEvaluate';
import ContentEvaluateReport, {
  loader as contentEvaluateReportLoader,
} from './ContentEvaluateReport/ContentEvaluateReport';
import Terms from './Terms/Terms';
import Faq from './Terms/Faq';
import Chats from './Chats/Chats';
import Chat, { loader as chatLoader } from './Chat/Chat';
import CreateChat from './CreateChat/CreateChat';
import CreateContact from './CreateContact/CreateContact';
import CreateGroupSetDetails from './CreateGroupSetDetails/CreateGroupSetDetails';
import CreateChatSelectContacts from './CreateChatSelectContacts/CreateChatSelectContacts';
import CreateChannelSetDetails from './CreateChannelSetDetails/CreateChannelSetDetails';
import LoginPlease from './LoginPlease/LoginPlease';
import ErrorPage from './ErrorPage/ErrorPage';
import ForwardChatsList, {
  loader as messageForwardLoader,
} from './ForwardChatsList/ForwardChatsList';
import ChatInfo from './ChatInfo/ChatInfo';
import AddParticipant, {
  loader as chatIdLoader,
} from './AddParticipant/AddParticipant';
import ForwardContentSelectChat from './ForwardContentSelectChat/ForwardContentSelectChat';
import ContentInteractions, {
  loader as contentInteractionLoader,
} from './ContentInteractions/ContentInteractions';
import Meet, { loader as meetLoader } from './Meet/Meet';
import AdminHomePage from './Admin/HomePage/HomePage';
import Announcement from './Announcement/Announcement';
import AnnouncementList from './Admin/AnnouncementList/AnnouncementList';
import AnnouncementCreate from './Admin/AnnouncementCreate/AnnouncementCreate';
import AnnouncementView, {
  loader as announcementViewLoader,
} from './Admin/AnnouncementView/AnnouncementView';
import AnnouncementUpdate, {
  loader as announcementUpdateLoader,
} from './Admin/AnnouncementUpdate/AnnouncementUpdate';
import OrganizationList from './Admin/OrganizationList/OrganizationList';
import OrganizationGraph from './Admin/OrganizationGraph/OrganizationGraph';
import OrganizationCreate from './Admin/OrganizationCreate/OrganizationCreate';
import OrganizationView, {
  loader as organizationViewLoader,
} from './Admin/OrganizationView/OrganizationView';
import OrganizationUpdate, {
  loader as organizationUpdateLoader,
} from './Admin/OrganizationUpdate/OrganizationUpdate';
import GroupMsgList from './Admin/GroupMsgList/GroupMsgList';
import GroupMsgCreate from './Admin/GroupMsgCreate/GroupMsgCreate';
import GroupMsgView, {
  loader as groupMsgViewLoader,
} from './Admin/GroupMsgView/GroupMsgView';
import AdminRequired from '../components/AdminRequired/AdminRequired';
import OrganizationUserList from './Admin/OrganizationUserList/OrganizationUserList';
import MarfookAdmin from './MarfookAdmin';
import MarfookFolder from './MarfookFolder';
import ContentMarfookPage, {
  loader as contentMarfookLoader,
} from './ContentMarfook/ContentMarfook';
import MarfookUserList from './Admin/MarfookUserList/MarfookUserList';
import MarfookInstructions from './Admin/MarfookInstructions/MarfookInstructions';
import MarfookInstructionCreate from './Admin/MarfookInstructionCreate/MarfookInstructionCreate';
import MarfookInstructionView from './Admin/MarfookInstructionView/MarfookInstructionView';
import MarfookInstructionEdit from './Admin/MarfookInstructionEdit/MarfookInstructionEdit';

export default createBrowserRouter([
  {
    path: PATHS.enter_mobile,
    errorElement: <ErrorPage />,
    element: <EnterMobile />,
  },
  {
    path: PATHS.enter_otp,
    errorElement: <ErrorPage />,
    element: <ValidateOTP />,
  },
  {
    path: PATHS.registration_form,
    errorElement: <ErrorPage />,
    element: (
      <AuthRequired>
        <RegisterationFrom />
      </AuthRequired>
    ),
  },
  {
    path: '/',
    element: <Layout />,
    errorElement: <ErrorPage />,
    children: [
      {
        path: PATHS.home,
        element: (
          <AuthRequired>
            <Home />
          </AuthRequired>
        ),
      },
      {
        path: PATHS.createContent,
        element: (
          <AuthRequired>
            <CreateContent />
          </AuthRequired>
        ),
      },
      {
        path: PATHS.updateContent,
        element: (
          <AuthRequired>
            <UpdateContent />
          </AuthRequired>
        ),
        loader: contentUpdateLoader,
      },
      {
        path: PATHS.content,
        element: (
          <AuthRequired>
            <Content />
          </AuthRequired>
        ),
        loader: contentLoader,
      },
      {
        path: PATHS.contentMarfook,
        element: (
          <AuthRequired>
            <ContentMarfookPage />
          </AuthRequired>
        ),
        loader: contentMarfookLoader,
      },
      {
        path: PATHS.marfook,
        element: (
          <AuthRequired>
            <MarfookFolder />
          </AuthRequired>
        ),
      },
      {
        path: PATHS.marfookAdmin,
        element: (
          <AuthRequired>
            <MarfookAdmin />
          </AuthRequired>
        ),
      },
      {
        path: PATHS.contentReels,
        element: (
          <AuthRequired>
            <ContentReels />
          </AuthRequired>
        ),
        loader: contentReelsLoader,
      },
      {
        path: PATHS.contentAnalytics,
        element: (
          <AuthRequired>
            <ContentAnalytics />
          </AuthRequired>
        ),
        loader: contentAnalyticsLoader,
      },
      {
        path: PATHS.contentEvaluateReport,
        element: (
          <AuthRequired>
            <ContentEvaluateReport />
          </AuthRequired>
        ),
        loader: contentEvaluateReportLoader,
      },
      {
        path: PATHS.contentEvaluate,
        element: (
          <AuthRequired>
            <ContentEvaluate />
          </AuthRequired>
        ),
        loader: contentEvaluateLoader,
      },
      {
        path: PATHS.forwardContent,
        element: (
          <AuthRequired>
            <ForwardContentSelectChat />
          </AuthRequired>
        ),
        loader: contentLoader,
      },
      {
        path: PATHS.contentInteractions,
        element: (
          <AuthRequired>
            <ContentInteractions />
          </AuthRequired>
        ),
        loader: contentInteractionLoader,
      },

      {
        path: PATHS.profile,
        element: (
          <AuthRequired>
            <Profile />
          </AuthRequired>
        ),
        loader: profileLoader,
      },
      // {
      //   path: PATHS.profileAnalytics,
      //   element: <ProfileAnalytics />,
      //   loader: profileAnalyticsLoader,
      // },

      {
        path: PATHS.search,
        element: (
          <AuthRequired>
            <Search />
          </AuthRequired>
        ),
        loader: searchLoader,
      },
      {
        path: PATHS.settings,
        element: (
          <AuthRequired>
            <Settings />
          </AuthRequired>
        ),
      },
      {
        path: PATHS.announcement,
        element: (
          <AuthRequired>
            <Announcement />
          </AuthRequired>
        ),
      },
      {
        path: PATHS.terms,
        element: <Terms />,
      },
      {
        path: PATHS.faq,
        element: <Faq />,
      },

      {
        path: PATHS.chatInfo,
        element: (
          <AuthRequired>
            <ChatInfo />
          </AuthRequired>
        ),
        loader: chatLoader,
      },
      {
        path: PATHS.createChat,
        element: (
          <AuthRequired>
            <CreateChat />
          </AuthRequired>
        ),
      },
      {
        path: PATHS.createChatSelectUser,
        element: (
          <AuthRequired>
            <CreateChatSelectContacts />
          </AuthRequired>
        ),
      },
      {
        path: PATHS.createGroupSetDetail,
        element: (
          <AuthRequired>
            <CreateGroupSetDetails />
          </AuthRequired>
        ),
      },
      {
        path: PATHS.createChannelSetDetail,
        element: (
          <AuthRequired>
            <CreateChannelSetDetails />
          </AuthRequired>
        ),
      },
      {
        path: PATHS.forwardChatsList,
        element: (
          <AuthRequired>
            <ForwardChatsList />
          </AuthRequired>
        ),
        loader: messageForwardLoader,
      },
      {
        path: PATHS.addParticipant,
        element: (
          <AuthRequired>
            <AddParticipant />
          </AuthRequired>
        ),
        loader: chatIdLoader,
      },

      {
        path: PATHS.createContact,
        element: (
          <AuthRequired>
            <CreateContact />
          </AuthRequired>
        ),
      },

      {
        path: PATHS.login_please,
        element: <LoginPlease />,
      },
    ],
  },
  {
    path: '/admin-panel',
    errorElement: <ErrorPage />,
    element: <AdminLayout />,
    children: [
      {
        path: PATHS.admin.homepage,
        element: (
          <AuthRequired>
            <AdminRequired>
              <AdminHomePage />
            </AdminRequired>
          </AuthRequired>
        ),
      },
      {
        path: PATHS.admin.organizationGraph,
        element: (
          <AuthRequired>
            <AdminRequired>
              <OrganizationGraph />
            </AdminRequired>
          </AuthRequired>
        ),
      },
      {
        path: PATHS.admin.organizationList,
        element: (
          <AuthRequired>
            <AdminRequired>
              <OrganizationList />
            </AdminRequired>
          </AuthRequired>
        ),
      },
      {
        path: PATHS.admin.organizationUserList,
        element: (
          <AuthRequired>
            <AdminRequired>
              <OrganizationUserList />
            </AdminRequired>
          </AuthRequired>
        ),
      },
      {
        path: PATHS.admin.organizationCreate,
        element: (
          <AuthRequired>
            <AdminRequired>
              <OrganizationCreate />
            </AdminRequired>
          </AuthRequired>
        ),
      },
      {
        path: PATHS.admin.organizationUpdate,
        element: (
          <AuthRequired>
            <AdminRequired>
              <OrganizationUpdate />
            </AdminRequired>
          </AuthRequired>
        ),
        loader: organizationUpdateLoader,
      },
      {
        path: PATHS.admin.organizationView,
        element: (
          <AuthRequired>
            <AdminRequired>
              <OrganizationView />
            </AdminRequired>
          </AuthRequired>
        ),
        loader: organizationViewLoader,
      },

      {
        path: PATHS.admin.announcementList,
        element: (
          <AuthRequired>
            <AdminRequired>
              <AnnouncementList />
            </AdminRequired>
          </AuthRequired>
        ),
      },
      {
        path: PATHS.admin.announcementCreate,
        element: (
          <AuthRequired>
            <AdminRequired>
              <AnnouncementCreate />
            </AdminRequired>
          </AuthRequired>
        ),
      },
      {
        path: PATHS.admin.announcementUpdate,
        element: (
          <AuthRequired>
            <AdminRequired>
              <AnnouncementUpdate />
            </AdminRequired>
          </AuthRequired>
        ),
        loader: announcementUpdateLoader,
      },
      {
        path: PATHS.admin.announcementView,
        element: (
          <AuthRequired>
            <AdminRequired>
              <AnnouncementView />
            </AdminRequired>
          </AuthRequired>
        ),
        loader: announcementViewLoader,
      },

      {
        path: PATHS.admin.groupMsgList,
        element: (
          <AuthRequired>
            <AdminRequired>
              <GroupMsgList />
            </AdminRequired>
          </AuthRequired>
        ),
      },
      {
        path: PATHS.admin.groupMsgCreate,
        element: (
          <AuthRequired>
            <AdminRequired>
              <GroupMsgCreate />
            </AdminRequired>
          </AuthRequired>
        ),
      },
      {
        path: PATHS.admin.groupMsgView,
        element: (
          <AuthRequired>
            <AdminRequired>
              <GroupMsgView />
            </AdminRequired>
          </AuthRequired>
        ),
        loader: groupMsgViewLoader,
      },
      // Marfook
      {
        path: PATHS.admin.marfookUserList,
        element: (
          <AuthRequired>
            <AdminRequired>
              <MarfookUserList />
            </AdminRequired>
          </AuthRequired>
        ),
      },
      {
        path: PATHS.admin.marfookInstructions,
        element: (
          <AuthRequired>
            <AdminRequired>
              <MarfookInstructions />
            </AdminRequired>
          </AuthRequired>
        ),
      },
      {
        path: PATHS.admin.marfookInstructionCreate,
        element: (
          <AuthRequired>
            <AdminRequired>
              <MarfookInstructionCreate />
            </AdminRequired>
          </AuthRequired>
        ),
      },
      {
        path: PATHS.admin.marfookInstructionView,
        element: (
          <AuthRequired>
            <AdminRequired>
              <MarfookInstructionView />
            </AdminRequired>
          </AuthRequired>
        ),
      },
      {
        path: PATHS.admin.marfookInstructionEdit,
        element: (
          <AuthRequired>
            <AdminRequired>
              <MarfookInstructionEdit />
            </AdminRequired>
          </AuthRequired>
        ),
      },
    ],
  },
  {
    path: PATHS.chat,
    element: <Chat />,
    errorElement: <ErrorPage />,
    loader: chatLoader,
  },
  {
    path: PATHS.chats,
    errorElement: <ErrorPage />,
    element: (
      <AuthRequired>
        <Chats />
      </AuthRequired>
    ),
  },

  {
    path: PATHS.meet,
    errorElement: <ErrorPage />,
    element: <Meet />,
    loader: meetLoader,
  },
]);
