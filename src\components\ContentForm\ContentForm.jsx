import {
  <PERSON>,
  But<PERSON>,
  Divider,
  <PERSON>rid,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>ack,
  <PERSON>Field,
  Typography,
} from '@mui/material';
import { useEffect, useRef, useState } from 'react';
import { useMutation, useQuery, useQueryClient } from 'react-query';
import { useNavigate } from 'react-router-dom';
import {
  updateContent,
  createContent,
  createInteractionRequest,
  deleteInteractionRequest as deleteInteractionRequestAPI,
} from 'apis/content';
import { useDispatch, useSelector } from 'react-redux';
import { setSnackbar } from 'store/layout';
import { isFile, useIsDesktop } from 'utils';
import CircularProgressWithValue from 'components/CircularProgressWithValue/CircularProgressWithValue';
import HighlightOffIcon from '@mui/icons-material/HighlightOff';
import { PATHS } from 'constants';
import DesktopAppBar from 'components/DesktopAppBar/DesktopAppBar';
import ContentPasteOutlinedIcon from '@mui/icons-material/ContentPasteOutlined';
import { selectMe } from 'store/auth';
import InteractionRequest from 'components/InteractionRequest/InteractionRequest';
import Category from './components/Category/Category';
import File from './components/File/File';
import Type from './components/Type/Type';
import MyBackdrop from './components/MyBackdrop/MyBackdrop';
import Labels from './components/Labels/Labels';
import IsOpenLayer from './components/IsOpenLayer/IsOpenLayer';
import IsElected from './components/IsElected/IsElected';
import SupportOriginalPost from './components/SupportOriginalPost/SupportOriginalPost';
import { GET_ME_URL, getMe, getUser } from '../../apis/auth';
import LoadingPage from '../LoadingPage/LoadingPage';

function createForm(isCreate, formEvent, filePreview) {
  const formData = new FormData();
  const {
    file,
    type,
    category,
    title,
    description,
    labels,
    isOpenLayer,
    elected,
    elected_hours,
    elected_date,
  } = formEvent.target;

  formData.append(type.name, type.value);
  formData.append(category.name, category.value);
  formData.append(title.name, title.value);
  formData.append(description.name, description.value);
  formData.append(labels.name, labels.value);
  if (isOpenLayer) formData.append(isOpenLayer?.id, isOpenLayer?.checked);
  formData.append(elected?.name, elected?.checked);
  if (elected_hours) {
    const hoursValue = elected_hours.value.split(',');
    for (let i = 0; i < hoursValue.length; i += 1) {
      formData.append(`elected_hours[${i}]`, hoursValue[i]);
    }
  }
  if (elected_date)
    formData.append(
      elected_date?.name,
      elected_date?.value.replace(
        /[\u200E\u200F\u202A-\u202E\u2066-\u2069]/g,
        '',
      ),
    );
  if (filePreview) formData.append('preview', filePreview);

  if (isCreate) {
    if (file.files.length) formData.append(file.name, file.files[0]);
  }

  return formData;
}

const EMPTY_CONTENT = {
  type: '',
  category: '',
  title: '',
  description: '',
  interaction_requests: [],
};

const deleteInteractionRequest = async (
  queryClient,
  contentId,
  interactionRequest,
  setInteractionRequests,
) => {
  if (interactionRequest.id) {
    await deleteInteractionRequestAPI(contentId, interactionRequest.id);

    queryClient.invalidateQueries({
      queryKey: [PATHS.content, contentId],
    });
  }
  setInteractionRequests(prevRequests =>
    prevRequests.filter(pr => pr.url !== interactionRequest.url),
  );
};

const submitInteractionRequest = async (contentId, interactionRequests) => {
  await Promise.all(
    interactionRequests.map(async ir =>
      createInteractionRequest({
        contentId,
        url: ir.url,
        followRequest: ir.follow_request,
        likeRequest: ir.like_request,
        commentRequest: ir.comment_request,
      }),
    ),
  );
};

export default function ContentForm({ content = EMPTY_CONTENT }) {
  // The form is either for create or update. If content is given,
  // it is for update. If not it is for create
  const isCreate = content === EMPTY_CONTENT;

  const descriptionRef = useRef(null);

  const isDesktop = useIsDesktop();

  const [filePreview, setFilePreview] = useState(null);
  const [fileType, setFileType] = useState(undefined);
  const [interactionRequests, setInteractionRequests] = useState([]);

  const navigate = useNavigate();

  const [progress, setProgress] = useState(0);
  const [loading, setLoading] = useState(false);

  const dispatch = useDispatch();
  const queryClient = useQueryClient();
  const abortController = useRef(null);
  const mutation = useMutation(
    async ({ event }) => {
      setLoading(true);
      const formData = createForm(true, event, filePreview);
      const result = isCreate
        ? await createContent(formData, setProgress, abortController.current)
        : await updateContent(content.id, formData);
      return result;
    },
    {
      onSuccess: async data => {
        await submitInteractionRequest(data.data.id, interactionRequests);

        const succMsg = isCreate
          ? 'محتوا با موفقیت اضافه شد'
          : 'محتوا با موفقیت ویرایش شد';
        dispatch(setSnackbar({ message: succMsg, severity: 'success' }));
        navigate(-1);
        queryClient.invalidateQueries('contents');
        queryClient.invalidateQueries({
          queryKey: [PATHS.content, content.id],
        });
      },
      onError: error => {
        if (error?.response?.status === 400) {
          const errorMsg = isCreate
            ? 'خطا در ساخت محتوا'
            : 'خطا در ویرایش محتوا';
          dispatch(setSnackbar({ message: errorMsg, severity: 'error' }));
        } else {
          dispatch(
            setSnackbar({ message: error?.response, severity: 'error' }),
          );
        }
      },
      onSettled: () => {
        setProgress(0);
        setLoading(false);
      },
    },
  );

  const errors = mutation.error?.response?.data || {};

  const getFilePreview = () => {
    if (filePreview) return URL.createObjectURL(filePreview);
    if (content.preview) return content.preview;
    return null;
  };

  const submitForm = event => {
    abortController.current = new AbortController();
    event.preventDefault();
    mutation.mutate({ event });
  };
  useEffect(
    () => () => {
      abortController.current?.abort();
    },
    [],
  );
  const cancelCreate = () => {
    abortController.current.abort();
  };

  const pasteDescription = async () => {
    descriptionRef.current.value = await navigator.clipboard.readText();
  };

  const { isLoading, data } = useQuery([PATHS.profile], () => getMe());

  if (isLoading) return <LoadingPage />;
  const me = data.data;
  const DIVIDER_MY = 3;

  return (
    <>
      {isDesktop && <DesktopAppBar />}

      <Box
        sx={{
          height: '100%',
          overflowY: 'scroll',
          mt: 2,
          pt: 1,
        }}
        className="no-scrollbar"
      >
        <form onSubmit={submitForm}>
          <Grid container columnSpacing={2}>
            <Grid item xs={12} lg={6}>
              <File
                file={content.file}
                fileType={content.file_type}
                errors={errors.file}
                disabled={!isCreate || mutation.isLoading}
                setPreview={setFilePreview}
                preview={getFilePreview()}
                setFileType={setFileType}
              />
            </Grid>
            <Grid item xs={12} lg={6} sx={{ mt: isDesktop ? 0 : 2 }}>
              <Type
                type={content.type}
                errors={errors.type}
                disabled={mutation.isLoading}
                selectedFileType={fileType}
              />

              <Category
                value={content.category}
                errors={errors.category}
                disabled={mutation.isLoading}
              />

              {(isFile(fileType) || isFile(content.file_type)) && (
                <IsOpenLayer
                  defaultChecked={content.is_open_layer}
                  key={content.id}
                />
              )}

              <Divider sx={{ mt: DIVIDER_MY, mb: DIVIDER_MY }} />

              <TextField
                variant="outlined"
                required
                label="عنوان"
                name="title"
                defaultValue={content.title}
                helperText={errors.title}
                error={!!errors.title}
                disabled={mutation.isLoading}
                inputProps={{ maxLength: 100 }}
                fullWidth
              />

              <TextField
                sx={{ mt: 2 }}
                label="توضیحات"
                name="description"
                defaultValue={content.description}
                variant="outlined"
                multiline
                rows={4}
                helperText={errors.description}
                disabled={mutation.isLoading}
                fullWidth
                inputRef={descriptionRef}
                InputProps={{
                  endAdornment: (
                    <IconButton edge="end" onClick={pasteDescription}>
                      <ContentPasteOutlinedIcon />
                    </IconButton>
                  ),
                }}
              />

              <Divider sx={{ mt: DIVIDER_MY, mb: DIVIDER_MY }} />

              {/* {me.can_elect_content && (
                <>
                  <IsElected
                    elected={content.elected}
                    electedHours={content.elected_hours}
                    electedDate={content.elected_date}
                  />
                  <Divider sx={{ mt: DIVIDER_MY, mb: DIVIDER_MY }} />
                </>
              )} */}

              <Labels defaultValue={content.labels} />

              <Divider sx={{ mt: DIVIDER_MY, mb: DIVIDER_MY }} />

              {!!me && !!me.can_see_admin_panel && (
                <SupportOriginalPost
                  addInteractionRequest={ir => {
                    setInteractionRequests(prevRequests => {
                      const exists = prevRequests.find(r => r.url === ir.url);
                      if (exists) {
                        dispatch(
                          setSnackbar({
                            message: 'رکورد تکراری!',
                            severity: 'error',
                          }),
                        );
                        return prevRequests;
                      }
                      if (
                        ir.comment_request ||
                        ir.follow_request ||
                        ir.like_request
                      ) {
                        return [ir, ...prevRequests];
                      }
                      dispatch(
                        setSnackbar({
                          message: 'نوع حمایت را انتخاب کنید!',
                          severity: 'error',
                        }),
                      );
                      return prevRequests;
                    });
                  }}
                />
              )}

              {[...interactionRequests, ...content.interaction_requests].map(
                ir => (
                  <InteractionRequest
                    sx={{ mt: 1 }}
                    id={ir.id}
                    url={ir.url}
                    followRequest={ir.follow_request}
                    likeRequest={ir.like_request}
                    commentRequest={ir.comment_request}
                    onDelete={() =>
                      deleteInteractionRequest(
                        queryClient,
                        content.id,
                        ir,
                        setInteractionRequests,
                      )
                    }
                  />
                ),
              )}

              <Typography sx={{ mt: 2, mb: 2, color: 'red', fontSize: '14px' }}>
                {errors.url}
              </Typography>

              <Divider sx={{ mt: DIVIDER_MY, mb: DIVIDER_MY }} />

              <Button
                variant="contained"
                size="large"
                type="submit"
                fullWidth
                sx={{ mt: 2 }}
              >
                ارسال
              </Button>
            </Grid>
          </Grid>
        </form>

        <MyBackdrop open={loading}>
          <Stack spacing={2}>
            <CircularProgressWithValue
              variant={isCreate ? 'determinate' : 'indeterminate'}
              value={progress}
              color="inherit"
              size={84}
            />
            <Button
              color="error"
              variant="contained"
              disableElevation
              startIcon={<HighlightOffIcon />}
              onClick={cancelCreate}
            >
              لغو
            </Button>
          </Stack>
        </MyBackdrop>
      </Box>
    </>
  );
}
