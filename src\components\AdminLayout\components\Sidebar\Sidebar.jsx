import {
  Divider,
  Grid,
  Stack,
  SvgIcon,
  Tooltip,
  Typography,
} from '@mui/material';
import ArrowLeftIcon from '@mui/icons-material/ArrowBackIos';
import { useLocation, useNavigate } from 'react-router-dom';
import { PATHS } from 'constants';
import { ReactComponent as BellSimpleIcon } from 'static/icons/BellSimple.svg';
import { ReactComponent as ChatTextIcon } from 'static/icons/ChatText.svg';
import { ReactComponent as ListPlusIcon } from 'static/icons/ListPlus.svg';
import { ReactComponent as ListBulletsIcon } from 'static/icons/ListBullets.svg';
import CorporateFareIcon from '@mui/icons-material/CorporateFare';
import GroupIcon from '@mui/icons-material/Group';
import {
  AccountTree,
  Dashboard,
  KeyboardDoubleArrowLeft,
  KeyboardDoubleArrowRight,
} from '@mui/icons-material';
import ArrowBackIosNewIcon from '@mui/icons-material/ArrowBackIosNew';
import { useDispatch, useSelector } from 'react-redux';
import { selectMe } from 'store/auth';
import IconSax from '../../../IconSax/IconSax';
import MyLink from '../../../MyLink/MyLink';
import {
  setSidebarCollapsed,
  selectSidebarCollapsed,
} from '../../../../store/layout';

function ExternalLinkButton({ to, children }) {
  return (
    <MyLink to={to} target="_blank" rel="noopener noreferrer">
      <Stack
        direction="row"
        justifyContent="space-between"
        alignItems="center"
        sx={{
          bgcolor: '#C4F8F3',
          borderRadius: '8px',
          pr: 2,
          pl: 2,
          mt: '20px',
          height: '54px',
        }}
      >
        {children}
      </Stack>
    </MyLink>
  );
}

export default function Sidebar() {
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useDispatch();
  const collapsed = useSelector(selectSidebarCollapsed);
  const me = useSelector(selectMe);

  const titleStyle = {
    color: '#0FA6A1',
    fontSize: '16px',
    fontWeight: 'bold',
    pt: 1,
  };
  const descriptionStyle = {
    color: '#A0A0A0',
    fontSize: '12px',
    pt: 1,
  };
  const menuIconStyle = {
    width: '40px',
    height: '40px',
    padding: '8px',
    color: '#868686',
  };

  return (
    <Grid
      container
      sx={{
        position: 'fixed!important',
        overflow: 'scroll!important',
        py: 1,
        px: 3,
        background: 'white',
        boxShadow: '0px 2px 20px 0px #00000012',
        width: !collapsed ? '320px' : '100px',
        height: 'calc(100vh - 56px)',
        maxHeight: 'calc(100vh - 56px)',
        alignItems: 'center',
      }}
      className="no-scrollbar"
    >
      <Stack width="100%">
        <Stack
          sx={{
            position: !collapsed ? 'absolute' : 'block',
            right: '25px',
            top: '15px',
            borderRadius: '100px',
            background: '#11a6a1',
            p: '2px',
            px: 2,
            color: 'white',
            cursor: 'pointer',
            textAlign: 'center',
            mb: 1,
          }}
          // onClick={() => setCollapsed(!collapsed)}
          onClick={() => dispatch(setSidebarCollapsed(!collapsed))}
        >
          {!collapsed ? (
            <KeyboardDoubleArrowRight />
          ) : (
            <KeyboardDoubleArrowLeft />
          )}
        </Stack>
        {!collapsed && (
          <>
            <Typography sx={titleStyle}>داشبورد</Typography>
            <Typography sx={descriptionStyle}>
              مشاهده گزارشات و آمار کلی سازمان و زیرسازمان های مجموعه
            </Typography>
          </>
        )}
        <Stack
          sx={{
            width: '100%',
            padding: '8px',
            cursor: 'pointer',
            borderRadius: '8px',
            background:
              location.pathname === PATHS.admin.homepage ? '#E7EAF4' : 'white',
          }}
          direction="row"
          justifyContent="space-between"
          alignItems="center"
          onClick={() => navigate(PATHS.admin.homepage)}
        >
          <Stack direction="row" justifyContent="left" alignItems="center">
            {!collapsed ? (
              <>
                <SvgIcon component={Dashboard} sx={menuIconStyle} />
                <Typography sx={{ fontSize: '14px' }}>داشبورد</Typography>
              </>
            ) : (
              <Tooltip title="داشبورد" arrow placement="right">
                <SvgIcon component={Dashboard} sx={menuIconStyle} />
              </Tooltip>
            )}
          </Stack>
          {/*{!collapsed && (*/}
          {/*  <Stack direction="row" justifyContent="right" alignItems="center">*/}
          {/*    <ArrowLeftIcon sx={{ fontSize: '18px', color: '#757575' }} />*/}
          {/*  </Stack>*/}
          {/*)}*/}
        </Stack>
      </Stack>
      {me && me?.groups?.includes('MarfookManager') && (
        <Stack width="100%">
          <Divider style={{ marginTop: '5px' }} />
          {!collapsed && (
            <>
              <Typography sx={titleStyle}>مرفوک</Typography>
              <Typography sx={descriptionStyle}>
                تنظیمات مربوط به مرفوک
              </Typography>
            </>
          )}
          <Stack
            sx={{
              width: '100%',
              padding: '8px',
              cursor: 'pointer',
              borderRadius: '8px',
              background:
                location.pathname === PATHS.admin.marfookUserList
                  ? '#E7EAF4'
                  : 'white',
            }}
            direction="row"
            justifyContent="space-between"
            alignItems="center"
            onClick={() => navigate(PATHS.admin.marfookUserList)}
          >
            <Stack direction="row" justifyContent="left" alignItems="center">
              {!collapsed ? (
                <>
                  <SvgIcon component={GroupIcon} sx={menuIconStyle} />
                  <Typography sx={{ fontSize: '14px' }}>مدیریت اعضا</Typography>
                </>
              ) : (
                <Tooltip title="مدیریت اعضا" arrow placement="right">
                  <SvgIcon component={GroupIcon} sx={menuIconStyle} />
                </Tooltip>
              )}
            </Stack>
          </Stack>

          <Stack
            sx={{
              width: '100%',
              padding: '8px',
              cursor: 'pointer',
              borderRadius: '8px',
              background:
                location.pathname === PATHS.admin.marfookInstructions
                  ? '#E7EAF4'
                  : 'white',
            }}
            direction="row"
            justifyContent="space-between"
            alignItems="center"
            onClick={() => navigate(PATHS.admin.marfookInstructions)}
          >
            <Stack direction="row" justifyContent="left" alignItems="center">
              {!collapsed ? (
                <>
                  <SvgIcon component={BellSimpleIcon} sx={menuIconStyle} />
                  <Typography sx={{ fontSize: '14px' }}>
                    تابلو اعلانات
                  </Typography>
                </>
              ) : (
                <Tooltip title="تابلو اعلانات" arrow placement="right">
                  <SvgIcon component={BellSimpleIcon} sx={menuIconStyle} />
                </Tooltip>
              )}
            </Stack>
          </Stack>
        </Stack>
      )}
      <Stack width="100%">
        <Divider style={{ marginTop: '5px' }} />
        {!collapsed && (
          <>
            <Typography sx={titleStyle}>مدیریت سازمان‌ها</Typography>
            <Typography sx={descriptionStyle}>
              تنظیمات مربوط به ساختار سازمان‌ها، افزودن و جستجوی اعضا
            </Typography>
          </>
        )}
        {/*<Stack*/}
        {/*  sx={{*/}
        {/*    width: '100%',*/}
        {/*    marginTop: '20px',*/}
        {/*    padding: '8px',*/}
        {/*    cursor: 'pointer',*/}
        {/*    borderRadius: '8px',*/}
        {/*    background:*/}
        {/*      location.pathname === PATHS.admin.organizationCreate*/}
        {/*        ? '#E7EAF4'*/}
        {/*        : 'white',*/}
        {/*  }}*/}
        {/*  direction="row"*/}
        {/*  justifyContent="space-between"*/}
        {/*  alignItems="center"*/}
        {/*  onClick={() => navigate(PATHS.admin.organizationCreate)}*/}
        {/*>*/}
        {/*  <Stack direction="row" justifyContent="left" alignItems="center">*/}
        {/*    <SvgIcon component={AddHomeWorkIcon} sx={menuIconStyle} />*/}
        {/*    <Typography sx={{ fontSize: '14px' }}>ایجاد سازمان</Typography>*/}
        {/*  </Stack>*/}
        {/*  <Stack direction="row" justifyContent="right" alignItems="center">*/}
        {/*    <ArrowLeftIcon sx={{ fontSize: '18px', color: '#757575' }} />*/}
        {/*  </Stack>*/}
        {/*</Stack>*/}
        <Stack
          sx={{
            width: '100%',
            padding: '8px',
            marginTop: '20px',
            cursor: 'pointer',
            borderRadius: '8px',
            background:
              location.pathname === PATHS.admin.organizationGraph
                ? '#E7EAF4'
                : 'white',
          }}
          direction="row"
          justifyContent="space-between"
          alignItems="center"
          onClick={() => navigate(PATHS.admin.organizationGraph)}
        >
          <Stack direction="row" justifyContent="left" alignItems="center">
            {!collapsed ? (
              <>
                <SvgIcon component={AccountTree} sx={menuIconStyle} />
                <Typography sx={{ fontSize: '14px' }}>
                  نمایش ساختار سازمانی
                </Typography>
              </>
            ) : (
              <Tooltip title="نمایش ساختار سازمانی" arrow placement="right">
                <SvgIcon component={AccountTree} sx={menuIconStyle} />
              </Tooltip>
            )}
          </Stack>
          {/*{!collapsed && (*/}
          {/*  <Stack direction="row" justifyContent="right" alignItems="center">*/}
          {/*    <ArrowLeftIcon sx={{ fontSize: '18px', color: '#757575' }} />*/}
          {/*  </Stack>*/}
          {/*)}*/}
        </Stack>
        <Stack
          sx={{
            width: '100%',
            padding: '8px',
            cursor: 'pointer',
            borderRadius: '8px',
            background:
              location.pathname === PATHS.admin.organizationList
                ? '#E7EAF4'
                : 'white',
          }}
          direction="row"
          justifyContent="space-between"
          alignItems="center"
          onClick={() => navigate(PATHS.admin.organizationList)}
        >
          <Stack direction="row" justifyContent="left" alignItems="center">
            {!collapsed ? (
              <>
                <SvgIcon component={CorporateFareIcon} sx={menuIconStyle} />
                <Typography sx={{ fontSize: '14px' }}>
                  لیست سازمان‌ها
                </Typography>
              </>
            ) : (
              <Tooltip title="لیست سازمان‌ها" arrow placement="right">
                <SvgIcon component={CorporateFareIcon} sx={menuIconStyle} />
              </Tooltip>
            )}
          </Stack>
          {/*{!collapsed && (*/}
          {/*  <Stack direction="row" justifyContent="right" alignItems="center">*/}
          {/*    <ArrowLeftIcon sx={{ fontSize: '18px', color: '#757575' }} />*/}
          {/*  </Stack>*/}
          {/*)}*/}
        </Stack>
        <Stack
          sx={{
            width: '100%',
            padding: '8px',
            cursor: 'pointer',
            borderRadius: '8px',
            background:
              location.pathname === PATHS.admin.organizationUserList
                ? '#E7EAF4'
                : 'white',
          }}
          direction="row"
          justifyContent="space-between"
          alignItems="center"
          onClick={() => navigate(PATHS.admin.organizationUserList)}
        >
          <Stack direction="row" justifyContent="left" alignItems="center">
            {!collapsed ? (
              <>
                <SvgIcon component={GroupIcon} sx={menuIconStyle} />
                <Typography sx={{ fontSize: '14px' }}>لیست اعضا</Typography>
              </>
            ) : (
              <Tooltip title="لیست اعضا" arrow placement="right">
                <SvgIcon component={GroupIcon} sx={menuIconStyle} />
              </Tooltip>
            )}
          </Stack>
          {/*{!collapsed && (*/}
          {/*  <Stack direction="row" justifyContent="right" alignItems="center">*/}
          {/*    <ArrowLeftIcon sx={{ fontSize: '18px', color: '#757575' }} />*/}
          {/*  </Stack>*/}
          {/*)}*/}
        </Stack>
      </Stack>
      <Stack width="100%">
        <Divider style={{ marginTop: '5px' }} />
        {!collapsed && (
          <>
            <Typography sx={titleStyle}>تنظیمات پیام</Typography>
            <Typography sx={descriptionStyle}>
              تنظیمات مربوط به ارسال پیامک یا پیام‌رسان تام در این تنظیمات قرار
              دارد.
            </Typography>
          </>
        )}
        <Stack
          sx={{
            width: '100%',
            marginTop: '20px',
            padding: '8px',
            cursor: 'pointer',
            borderRadius: '8px',
            background:
              location.pathname === PATHS.admin.groupMsgCreate
                ? '#E7EAF4'
                : 'white',
          }}
          direction="row"
          justifyContent="space-between"
          alignItems="center"
          onClick={() => navigate(PATHS.admin.groupMsgCreate)}
        >
          <Stack direction="row" justifyContent="left" alignItems="center">
            {!collapsed ? (
              <>
                <SvgIcon component={ChatTextIcon} sx={menuIconStyle} />
                <Typography sx={{ fontSize: '14px' }}>ارسال پیام</Typography>
              </>
            ) : (
              <Tooltip title="ارسال پیام" arrow placement="right">
                <SvgIcon component={ChatTextIcon} sx={menuIconStyle} />
              </Tooltip>
            )}
          </Stack>
          {/*{!collapsed && (*/}
          {/*  <Stack direction="row" justifyContent="right" alignItems="center">*/}
          {/*    <ArrowLeftIcon sx={{ fontSize: '18px', color: '#757575' }} />*/}
          {/*  </Stack>*/}
          {/*)}*/}
        </Stack>
        <Stack
          sx={{
            width: '100%',
            padding: '8px',
            cursor: 'pointer',
            borderRadius: '8px',
            background:
              location.pathname === PATHS.admin.groupMsgList
                ? '#E7EAF4'
                : 'white',
          }}
          direction="row"
          justifyContent="space-between"
          alignItems="center"
          onClick={() => navigate(PATHS.admin.groupMsgList)}
        >
          <Stack direction="row" justifyContent="left" alignItems="center">
            {!collapsed ? (
              <>
                <SvgIcon component={ListBulletsIcon} sx={menuIconStyle} />
                <Typography sx={{ fontSize: '14px' }}>
                  مشاهده لیست پیام‌ها
                </Typography>
              </>
            ) : (
              <Tooltip title="مشاهده لیست پیام‌ها" arrow placement="right">
                <SvgIcon component={ListBulletsIcon} sx={menuIconStyle} />
              </Tooltip>
            )}
          </Stack>
          {/*{!collapsed && (*/}
          {/*  <Stack direction="row" justifyContent="right" alignItems="center">*/}
          {/*    <ArrowLeftIcon sx={{ fontSize: '18px', color: '#757575' }} />*/}
          {/*  </Stack>*/}
          {/*)}*/}
        </Stack>
      </Stack>
      {/* <Stack width={'100%'}> */}
      {/*  <Typography sx={titleStyle}>کاربران</Typography> */}
      {/*  <Typography sx={descriptionStyle}>تنظیمات مربوط به مخاطبین٬ اضافه٬ ویرایش و ... در رابطه با لیست مخاطبین را در اینجا قرار دارد.</Typography> */}
      {/*  <Stack */}
      {/*    sx={{ */}
      {/*      width: '100%', */}
      {/*      marginTop: '20px', */}
      {/*      padding: '8px', */}
      {/*      cursor: 'pointer' */}
      {/*    }} */}
      {/*    direction="row" */}
      {/*    justifyContent="space-between" */}
      {/*    alignItems="center" */}
      {/*    // onClick={() => navigate(PATHS.admin)} */}
      {/*  > */}
      {/*    <Stack direction="row" */}
      {/*           justifyContent="left" */}
      {/*           alignItems="center"> */}
      {/*      <SvgIcon component={UserPlusIcon} sx={menuIconStyle}/> */}
      {/*      <Typography sx={{fontSize: '14px',}}>دعوت کاربران جدید</Typography> */}
      {/*    </Stack> */}
      {/*    <Stack direction="row" */}
      {/*           justifyContent="right" */}
      {/*           alignItems="center"> */}
      {/*      <ArrowLeftIcon sx={{fontSize: '18px', color: '#757575'}}/> */}
      {/*    </Stack> */}
      {/*  </Stack> */}
      {/*  <Stack */}
      {/*    sx={{ */}
      {/*      width: '100%', */}
      {/*      padding: '8px', */}
      {/*      cursor: 'pointer' */}
      {/*    }} */}
      {/*    direction="row" */}
      {/*    justifyContent="space-between" */}
      {/*    alignItems="center" */}
      {/*    // onClick={() => navigate(PATHS.admin)} */}
      {/*  > */}
      {/*    <Stack direction="row" */}
      {/*           justifyContent="left" */}
      {/*           alignItems="center"> */}
      {/*      <SvgIcon component={UserListIcon} sx={menuIconStyle}/> */}
      {/*      <Typography sx={{fontSize: '14px',}}>لیست کاربران</Typography> */}
      {/*    </Stack> */}
      {/*    <Stack direction="row" */}
      {/*           justifyContent="right" */}
      {/*           alignItems="center"> */}
      {/*      <ArrowLeftIcon sx={{fontSize: '18px', color: '#757575'}}/> */}
      {/*    </Stack> */}
      {/*  </Stack> */}
      {/*  <Divider/> */}
      {/* </Stack> */}
      <Stack width="100%">
        <Divider style={{ marginTop: '5px' }} />
        {!collapsed && (
          <>
            <Typography sx={titleStyle}>تنظیمات اعلان</Typography>
            <Typography sx={descriptionStyle}>
              تنظیمات مربوط به صفحه اعلانات و ایجاد و ویرایش اعلانات
            </Typography>
          </>
        )}
        <Stack
          sx={{
            width: '100%',
            marginTop: '20px',
            padding: '8px',
            cursor: 'pointer',
            borderRadius: '8px',
            background:
              location.pathname === PATHS.admin.announcementCreate
                ? '#E7EAF4'
                : 'white',
          }}
          direction="row"
          justifyContent="space-between"
          alignItems="center"
          onClick={() => navigate(PATHS.admin.announcementCreate)}
        >
          <Stack direction="row" justifyContent="left" alignItems="center">
            {!collapsed ? (
              <>
                <SvgIcon component={BellSimpleIcon} sx={menuIconStyle} />
                <Typography sx={{ fontSize: '14px' }}>اعلان جدید</Typography>
              </>
            ) : (
              <Tooltip title="اعلان جدید" arrow placement="right">
                <SvgIcon component={BellSimpleIcon} sx={menuIconStyle} />
              </Tooltip>
            )}
          </Stack>
          {/*{!collapsed && (*/}
          {/*  <Stack direction="row" justifyContent="right" alignItems="center">*/}
          {/*    <ArrowLeftIcon sx={{ fontSize: '18px', color: '#757575' }} />*/}
          {/*  </Stack>*/}
          {/*)}*/}
        </Stack>
        <Stack
          sx={{
            width: '100%',
            padding: '8px',
            cursor: 'pointer',
            borderRadius: '8px',
            background:
              location.pathname === PATHS.admin.announcementList
                ? '#E7EAF4'
                : 'white',
          }}
          direction="row"
          justifyContent="space-between"
          alignItems="center"
          onClick={() => navigate(PATHS.admin.announcementList)}
        >
          <Stack direction="row" justifyContent="left" alignItems="center">
            {!collapsed ? (
              <>
                <SvgIcon component={ListPlusIcon} sx={menuIconStyle} />
                <Typography sx={{ fontSize: '14px' }}>لیست اعلانات</Typography>
              </>
            ) : (
              <Tooltip title="لیست اعلانات" arrow placement="right">
                <SvgIcon component={ListPlusIcon} sx={menuIconStyle} />
              </Tooltip>
            )}
          </Stack>
          {/*{!collapsed && (*/}
          {/*  <Stack direction="row" justifyContent="right" alignItems="center">*/}
          {/*    <ArrowLeftIcon sx={{ fontSize: '18px', color: '#757575' }} />*/}
          {/*  </Stack>*/}
          {/*)}*/}
        </Stack>
        <Divider style={{ marginTop: '5px' }} />

        {/* <ExternalLinkButton to="https://tam-front-production.s3.ir-thr-at1.arvanstorage.ir/tutorial%2Fadmin_tutorial.mp4?versionId=">
          <Typography
            color="black"
            sx={{
              height: '54px',
              lineHeight: '54px',
              fontSize: '14px',
              display: 'flex',
              alignItems: 'center',
            }}
          >
            {!collapsed ? (
              <>
                <IconSax
                  name="video-circle"
                  sx={{ color: '#000', mr: 1, height: '100%' }}
                />
                <Typography sx={{ fontSize: '16px' }}>
                  آشنایی با پنل تام
                </Typography>
              </>
            ) : (
              <Tooltip title="آشنایی با پنل تام" arrow placement="right">
                <IconSax
                  name="video-circle"
                  sx={{ color: '#000', mr: 1, height: '100%' }}
                />
              </Tooltip>
            )}
          </Typography>

          {!collapsed && (
            <Typography
              color="black"
              sx={{
                height: '54px',
                lineHeight: '54px',
                fontSize: '12px !important',
              }}
            >
              تماشای فیلم
              <ArrowBackIosNewIcon
                sx={{
                  fontSize: '18px !important',
                  verticalAlign: 'middle',
                  marginLeft: '4px',
                }}
              />
            </Typography>
          )}
        </ExternalLinkButton> */}
      </Stack>
      <Stack width="100%" sx={{ minHeight: collapsed ? '300px' : '0' }} />
    </Grid>
  );
}
