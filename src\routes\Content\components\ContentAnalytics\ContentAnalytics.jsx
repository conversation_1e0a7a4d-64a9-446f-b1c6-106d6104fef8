import { Stack, Typography } from '@mui/material';
import { ReactComponent as HeartIcon } from 'static/icons/heart.svg';
import { ReactComponent as HeartBoldIcon } from 'static/icons/heart-bold.svg';
import { ReactComponent as FavoriteIcon } from 'static/icons/archive-book.svg';
import { ReactComponent as FavoriteBoldIcon } from 'static/icons/archive-book-bold.svg';
import { FAVORITE, LIKE } from 'constants';
import VisibilityOutlinedIcon from '@mui/icons-material/VisibilityOutlined';
import DownloadOutlinedIcon from '@mui/icons-material/DownloadOutlined';
import { Box } from '@mui/system';
import { useSelector } from 'react-redux';
import InteractionButton from '../InteractionButton/InteractionButton';
import ForwardButton from '../ForwardButton/ForwardButton';
import ElectButton from '../ElectButton/ElectButton';
import { selectMe } from '../../../../store/auth';

function getInteraction(interactions, interactionType) {
  return interactions.find(i => i.type === interactionType);
}

export function ContentAnalytics({
  contentId,
  interactions,
  like,
  favorite,
  view,
  download,
  elected = false,
  evaluationRequested = false,
  total_score = 0,
  evaluator_count = 0,
}) {
  const me = useSelector(selectMe);
  // me.can_elect_content = undefined;

  return (
    <>
      <Stack
        direction="row"
        sx={{
          background: '#fff',
          pt: 1,
          marginTop: '0!important',
        }}
        justifyContent="space-between"
      >
        <Stack direction="row" alignItems="center" spacing={1}>
          <InteractionButton
            text={favorite}
            icon={FavoriteIcon}
            filledIcon={FavoriteBoldIcon}
            type={FAVORITE}
            interaction={getInteraction(interactions, FAVORITE)}
            contentId={contentId}
            authRequired
          />
          <InteractionButton
            text={like}
            icon={HeartIcon}
            filledIcon={HeartBoldIcon}
            type={LIKE}
            interaction={getInteraction(interactions, LIKE)}
            contentId={contentId}
            authRequired
          />

          <ForwardButton contentId={contentId} />

          <ElectButton
            elected={elected}
            evaluationRequested={evaluationRequested}
            contentId={contentId}
          />
        </Stack>

        <Stack
          direction="row"
          alignItems="center"
          spacing={2}
          sx={{ color: '#64676A', fontSize: '14px' }}
        >
          <Stack direction="row">
            <VisibilityOutlinedIcon />
            {view}
          </Stack>

          <Stack direction="row">
            <DownloadOutlinedIcon />
            {download}
          </Stack>
        </Stack>
      </Stack>

      {!!me &&
        !!me.can_elect_content &&
        (!!evaluationRequested || !!total_score) && (
          <Stack
            direction="row"
            justifyContent="center"
            alignItems="center"
            spacing={2}
            sx={{ pt: 1, marginTop: '0!important' }}
          >
            <Box sx={{ backgroundColor: '#E7EAF4', borderRadius: '24px' }}>
              <Typography
                sx={{
                  fontSize: '14px',
                  p: 1,
                  px: 3,
                }}
                key="label"
              >
                <strong>میانگین ارزیابی محتوا:</strong>
                <span
                  style={{ fontSize: '12px' }}
                >{`   (${total_score?.toFixed(2) || '۰'} از ۱۰)  `}</span>
                <strong
                  style={{ fontSize: '12px' }}
                >{`${evaluator_count || 0} نفر`}</strong>
              </Typography>
            </Box>
          </Stack>
        )}
    </>
  );
}
