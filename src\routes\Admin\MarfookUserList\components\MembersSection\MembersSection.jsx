import { useEffect, useState } from 'react';
import {
  Box,
  Button,
  FormControl,
  Grid,
  IconButton,
  Modal,
  Paper,
  Stack,
  styled,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Tooltip,
  Typography,
} from '@mui/material';
import {
  Check,
  Close,
  Delete,
  Key,
  PersonAdd,
  VerifiedUser,
} from '@mui/icons-material';
import { updateMarfookUser } from 'apis/marfook';
import { setSnackbar } from 'store/layout';
import { useDispatch } from 'react-redux';
import SearchInput from './components/SearchInput';
import BottomSheetMessage from '../../../../../components/BottomSheetMessage/BottomSheetMessage';
import BottomSheetPrimaryButton from '../../../../../components/BottomSheetPrimaryButton/BottomSheetPrimaryButton';
import BottomSheetSecondaryButton from '../../../../../components/BottomSheetSecondaryButton/BottomSheetSecondaryButton';
import BottomSheet from '../../../../../components/BottomSheet/BottomSheet';
import SelectMarfookUser from './components/MultiSelectNonAllocatedUsers';

const CTableHead = styled(TableHead)(() => ({
  '& th': {
    fontSize: '14px',
    color: '#737373',
    border: 'none',
  },
}));
const CTableRow = styled(TableRow)(() => ({
  '& td': {
    border: 0,
    color: '#222222',
    fontSize: '16px',
  },
}));

function MembersSection({ list = [] }) {
  const dispatch = useDispatch();

  const [open, setOpen] = useState(false);

  const [members, setMembers] = useState(list);

  const redStyle = {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    fontSize: '14px',
    background: '#d72121',
    width: '28px',
    height: '28px',
    borderRadius: '100px',
    color: 'white',
  };
  const greenStyle = {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    fontSize: '14px',
    background: '#11a6a1',
    width: '28px',
    height: '28px',
    borderRadius: '100px',
    color: 'white',
  };

  const [searchText, setSearchText] = useState('');
  const [addMemberError, setAddMemberError] = useState('');
  const [filteredData, setFilteredData] = useState(list);
  const [showDeleteBottomSheet, setShowDeleteBottomSheet] = useState(false);
  const [showPermisionModal, setShowPermisionModal] = useState(false);
  const [selectedMember, setSelectedMember] = useState(null);

  const [selectedMemberToAdd, setSelectedMemberToAdd] = useState({});

  const [userPermisions, setUserPermisions] = useState([]);

  const [addUserForm, setAddUserForm] = useState({ step: 1 });

  useEffect(() => {
    const filtered = members
      .filter(
        item =>
          item?.display_username
            ?.toLowerCase()
            .includes(searchText?.toLowerCase()) ||
          item?.last_name?.toLowerCase().includes(searchText?.toLowerCase()) ||
          item?.first_name?.toLowerCase().includes(searchText?.toLowerCase()),
      )
      .map(member => ({
        ...member,
      }));

    setFilteredData(filtered);
  }, [searchText, members]);
  const handleInputChange = e => {
    setSearchText(e.target.value);
  };
  const handleDelete = member => {
    setShowDeleteBottomSheet(true);
    setSelectedMember(member);
  };
  const handlePermissions = member => {
    setShowPermisionModal(true);
    setUserPermisions(member.groups);
    setSelectedMember(member);
  };

  const handleAddMember = async () => {
    try {
      if (selectedMemberToAdd.id) {
        const response = await updateMarfookUser(selectedMemberToAdd.id, {
          groups: userPermisions.filter(
            x =>
              x === 'MarfookManager' ||
              x === 'MarfookExpert' ||
              x === 'MarfookEvaluator',
          ),
        });
        dispatch(
          setSnackbar({
            message: 'کابر با موفقیت افزوده شد',
            severity: 'success',
          }),
        );
        setMembers(response.data || members);
        // window.location.reload();
      }
    } catch (e) {
      dispatch(
        setSnackbar({
          message: 'خطا در افزودن کاربر!',
          severity: 'error',
        }),
      );
    } finally {
      setAddUserForm({ step: 1 });
      setUserPermisions([]);
      setSelectedMemberToAdd(null);
      setSelectedMember(null);
      setOpen(false);
    }
  };

  const handleOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setAddUserForm({ step: 1 });
    setSelectedMemberToAdd(null);
    setSelectedMember(null);
    setOpen(false);
  };
  const handleClosePermisionModal = () => {
    setAddUserForm({ step: 1 });
    setSelectedMemberToAdd({});
    setSelectedMember(null);
    setShowPermisionModal(false);
  };

  const handleUserClick = async (type = 'user') => {
    if (!selectedMemberToAdd) return;
    setAddUserForm({ ...addUserForm, type, step: 2 });
  };

  const submitPermisions = async () => {
    try {
      if (selectedMember.id) {
        await updateMarfookUser(selectedMember.id, {
          groups: userPermisions.filter(
            x =>
              x === 'MarfookManager' ||
              x === 'MarfookExpert' ||
              x === 'MarfookEvaluator',
          ),
        });
        selectedMember.groups = userPermisions;
        dispatch(
          setSnackbar({
            message: 'دسترسی کاربر ویرایش شد.',
            severity: 'success',
          }),
        );
        // window.location.reload();
      }
    } catch (e) {
      dispatch(
        setSnackbar({
          message: 'خطا در ویرایش سطح دسترسی کاربر!',
          severity: 'error',
        }),
      );
    } finally {
      setUserPermisions([]);
      setShowPermisionModal(false);
    }
  };

  const deleteUser = async () => {
    try {
      if (selectedMember.id) {
        await updateMarfookUser(selectedMember.id, {
          groups: [],
        });
        setMembers(members.filter(member => member.id !== selectedMember.id));
        dispatch(
          setSnackbar({
            message: 'کابر از لیست مرفوک حذف شد',
            severity: 'success',
          }),
        );
        // window.location.reload();
      }
    } catch (e) {
      dispatch(
        setSnackbar({
          message: 'خطا در حذف کاربر!',
          severity: 'error',
        }),
      );
    } finally {
      setShowDeleteBottomSheet(false);
    }
  };

  const colTitles = ['عکس', 'نام عضو', 'مدیر', 'کارشناس', 'ارزیاب', 'اقدامات'];

  return (
    <Grid
      sx={{
        py: 2,
        px: 3,
        mb: 3,
        borderRadius: '8px',
        background: 'white',
        boxShadow: '0px 2px 20px 0px #00000012',
        width: '100%',
      }}
    >
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
          width: '100%',
          gap: '10px',
          paddingBottom: '10px',
          borderBottom: '1px solid #ccc',
        }}
      >
        <SearchInput
          label="جستجو"
          onChange={handleInputChange}
          sx={{ maxWidth: '400px', zIndex: '10' }}
        />
        <Button
          variant="contained"
          size="large"
          type="submit"
          sx={{ width: '180px' }}
          onClick={() => {
            handleOpen();
          }}
        >
          <PersonAdd sx={{ marginRight: '6px' }} />
          افزودن عضو
        </Button>
      </Box>
      <TableContainer>
        <Table>
          <CTableHead>
            <TableRow>
              {colTitles.map(title => (
                <TableCell>{title}</TableCell>
              ))}
            </TableRow>
          </CTableHead>
          <TableBody>
            {filteredData.map(row => (
              <CTableRow key={row.id}>
                <TableCell sx={{ maxWidth: '40px' }}>
                  <img
                    width="40px"
                    height="40px"
                    alt="avatar"
                    src={row.avatar || '/logo.png'}
                    style={{ borderRadius: '100px' }}
                  />
                </TableCell>
                <TableCell width="100%">
                  <span style={{ fontSize: '16px', fontWeight: 'bold' }}>
                    {row.first_name
                      ? `${row.first_name} ${row.last_name}`
                      : row.username}
                    {row.isAdmin && ' (مدیر) '}
                  </span>
                  <br />
                  <span style={{ fontSize: '14px' }}>
                    {row.display_username ? `@${row.display_username}` : ''}
                  </span>
                </TableCell>
                <TableCell width="100%">
                  {row.groups.includes('MarfookManager') ? (
                    <div style={greenStyle}>
                      <Check />
                    </div>
                  ) : (
                    <div style={redStyle}>
                      <Close />
                    </div>
                  )}
                </TableCell>
                <TableCell width="100%">
                  {row.groups.includes('MarfookExpert') ? (
                    <div style={greenStyle}>
                      <Check />
                    </div>
                  ) : (
                    <div style={redStyle}>
                      <Close />
                    </div>
                  )}
                </TableCell>
                <TableCell width="100%">
                  {row.groups.includes('MarfookEvaluator') ? (
                    <div style={greenStyle}>
                      <Check />
                    </div>
                  ) : (
                    <div style={redStyle}>
                      <Close />
                    </div>
                  )}
                </TableCell>

                <TableCell>
                  <Box
                    sx={{
                      display: 'flex',
                      flexDirection: 'row',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                  >
                    <Tooltip title="تغییر دسترسی" placement="top">
                      <IconButton
                        sx={{ display: 'flex' }}
                        onClick={() => handlePermissions(row)}
                      >
                        <Key />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="حذف از اعضای مرفوک" placement="top">
                      <IconButton
                        sx={{ display: 'flex' }}
                        onClick={() => handleDelete(row)}
                      >
                        <Delete />
                      </IconButton>
                    </Tooltip>
                  </Box>
                </TableCell>
              </CTableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      <Modal
        open={open}
        onClose={handleClose}
        aria-labelledby="child-modal-title"
        aria-describedby="child-modal-description"
      >
        <Grid
          container
          sx={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            pt: 2,
            px: 4,
            pb: 3,
            borderRadius: '8px',
            background: 'white',
            boxShadow: '0px 2px 20px 0px #00000012',
            width: '50%',
          }}
        >
          <Stack width="100%">
            <Typography
              align="center"
              fontWeight="bold"
              fontSize={18}
              sx={{ pb: 2, borderBottom: '1px solid #ccc' }}
            >
              افزودن عضو مرفوک
            </Typography>
            {addUserForm.step === 1 && (
              <>
                <Typography fontSize={16} sx={{ mt: 4 }}>
                  برای انتخاب عضو/اعضا جدید، نام یا شماره شخص مورد نظر را در
                  باکس زیر وارد نمایید:
                </Typography>
                <FormControl fullWidth sx={{ mt: 2, pb: 4 }}>
                  <SelectMarfookUser
                    setSelectedMember={setSelectedMemberToAdd}
                  />
                </FormControl>
                <Typography
                  align="center"
                  fontSize={14}
                  sx={{ mb: '45px', mt: 5, color: 'red' }}
                >
                  {addMemberError}
                </Typography>
                <Box
                  sx={{
                    position: 'absolute',
                    bottom: '0',
                    left: '0',
                    width: '100%',
                    px: 3,
                    py: 2,
                    display: 'flex',
                    flexDirection: 'row',
                    gap: '10px',
                  }}
                >
                  <Button
                    variant="contained"
                    size="large"
                    type="submit"
                    sx={{ mt: 2, width: '100%' }}
                    onClick={handleUserClick}
                  >
                    تایید
                  </Button>
                  <Button
                    variant="outlined"
                    size="large"
                    type="button"
                    sx={{ mt: 2, width: '50%' }}
                    onClick={handleClose}
                  >
                    انصراف
                  </Button>
                </Box>
              </>
            )}
            {addUserForm.step === 2 && (
              <>
                <Typography fontSize={16} sx={{ mt: 4 }}>
                  دسترسی کاربر را انتخاب نمایید:
                </Typography>
                <Box
                  sx={{
                    display: 'flex',
                    flexWrap: 'wrap',
                    alignItems: 'center',
                    mt: 3,
                    pb: 4,
                    justifyContent: 'center',
                    '& > :not(style)': {
                      display: 'flex',
                      m: 1,
                      width: 110,
                      height: 110,
                      cursor: 'pointer',
                      alignItems: 'center',
                      justifyContent: 'center',
                      boxShadow: 'none',
                      border: '1px solid #ccc',
                      color: '#bbb',
                      flexDirection: 'column',
                      fontSize: '14px',
                    },
                  }}
                >
                  <Paper
                    elevation={2}
                    onClick={() =>
                      setUserPermisions(
                        userPermisions.includes('MarfookManager')
                          ? userPermisions.filter(x => x !== 'MarfookManager')
                          : [...userPermisions, 'MarfookManager'],
                      )
                    }
                    style={{
                      backgroundColor: userPermisions.includes('MarfookManager')
                        ? '#11a6a1'
                        : '',
                      color: userPermisions.includes('MarfookManager')
                        ? '#333'
                        : '#bbb',
                    }}
                  >
                    <VerifiedUser sx={{ mr: '3px' }} />
                    مدیر مرفوک
                  </Paper>
                  <Paper
                    elevation={2}
                    onClick={() =>
                      setUserPermisions(
                        userPermisions.includes('MarfookExpert')
                          ? userPermisions.filter(x => x !== 'MarfookExpert')
                          : [...userPermisions, 'MarfookExpert'],
                      )
                    }
                    style={{
                      backgroundColor: userPermisions.includes('MarfookExpert')
                        ? '#11a6a1'
                        : '',
                      color: userPermisions.includes('MarfookExpert')
                        ? '#333'
                        : '#bbb',
                    }}
                  >
                    <PersonAdd sx={{ mr: '3px' }} />
                    کارشناس مرفوک
                  </Paper>
                  <Paper
                    elevation={2}
                    onClick={() =>
                      setUserPermisions(
                        userPermisions.includes('MarfookEvaluator')
                          ? userPermisions.filter(x => x !== 'MarfookEvaluator')
                          : [...userPermisions, 'MarfookEvaluator'],
                      )
                    }
                    style={{
                      backgroundColor: userPermisions.includes(
                        'MarfookEvaluator',
                      )
                        ? '#11a6a1'
                        : '',
                      color: userPermisions.includes('MarfookEvaluator')
                        ? '#333'
                        : '#bbb',
                    }}
                  >
                    <PersonAdd sx={{ mr: '3px' }} />
                    ارزیاب مرفوک
                  </Paper>
                </Box>
                <Button
                  onClick={handleAddMember}
                  variant="contained"
                  sx={{ width: '50%', margin: '0 auto' }}
                >
                  ثبت کاربر
                </Button>
              </>
            )}
          </Stack>
        </Grid>
      </Modal>

      <Modal
        open={showPermisionModal}
        onClose={handleClosePermisionModal}
        aria-labelledby="child-modal-title"
        aria-describedby="child-modal-description"
      >
        <Grid
          container
          sx={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            pt: 2,
            px: 4,
            pb: 3,
            borderRadius: '8px',
            background: 'white',
            boxShadow: '0px 2px 20px 0px #00000012',
            width: '50%',
          }}
        >
          <Stack width="100%">
            <Typography
              align="center"
              fontWeight="bold"
              fontSize={18}
              sx={{ pb: 2, borderBottom: '1px solid #ccc' }}
            >
              ویرایش دسترسی کاربر مرفوک{' '}
            </Typography>
            <Typography fontSize={16} sx={{ mt: 4 }}>
              دسترسی کاربر را انتخاب نمایید:
            </Typography>
            <Box
              sx={{
                display: 'flex',
                flexWrap: 'wrap',
                alignItems: 'center',
                mt: 3,
                pb: 4,
                justifyContent: 'center',
                '& > :not(style)': {
                  display: 'flex',
                  m: 1,
                  width: 110,
                  height: 110,
                  cursor: 'pointer',
                  alignItems: 'center',
                  justifyContent: 'center',
                  boxShadow: 'none',
                  border: '1px solid #ccc',
                  color: '#bbb',
                  flexDirection: 'column',
                  fontSize: '14px',
                },
              }}
            >
              <Paper
                elevation={2}
                onClick={() =>
                  setUserPermisions(
                    userPermisions.includes('MarfookManager')
                      ? userPermisions.filter(x => x !== 'MarfookManager')
                      : [...userPermisions, 'MarfookManager'],
                  )
                }
                style={{
                  backgroundColor: userPermisions.includes('MarfookManager')
                    ? '#11a6a1'
                    : '',
                  color: userPermisions.includes('MarfookManager')
                    ? '#333'
                    : '#bbb',
                }}
              >
                <VerifiedUser sx={{ mr: '3px' }} />
                مدیر مرفوک
              </Paper>
              <Paper
                elevation={2}
                onClick={() =>
                  setUserPermisions(
                    userPermisions.includes('MarfookExpert')
                      ? userPermisions.filter(x => x !== 'MarfookExpert')
                      : [...userPermisions, 'MarfookExpert'],
                  )
                }
                style={{
                  backgroundColor: userPermisions.includes('MarfookExpert')
                    ? '#11a6a1'
                    : '',
                  color: userPermisions.includes('MarfookExpert')
                    ? '#333'
                    : '#bbb',
                }}
              >
                <PersonAdd sx={{ mr: '3px' }} />
                کارشناس مرفوک
              </Paper>
              <Paper
                elevation={2}
                onClick={() =>
                  setUserPermisions(
                    userPermisions.includes('MarfookEvaluator')
                      ? userPermisions.filter(x => x !== 'MarfookEvaluator')
                      : [...userPermisions, 'MarfookEvaluator'],
                  )
                }
                style={{
                  backgroundColor: userPermisions.includes('MarfookEvaluator')
                    ? '#11a6a1'
                    : '',
                  color: userPermisions.includes('MarfookEvaluator')
                    ? '#333'
                    : '#bbb',
                }}
              >
                <PersonAdd sx={{ mr: '3px' }} />
                ارزیاب مرفوک
              </Paper>
            </Box>
            <Button
              onClick={() => submitPermisions()}
              variant="contained"
              sx={{ width: '50%', margin: '0 auto' }}
            >
              ثبت دسترسی
            </Button>
          </Stack>
        </Grid>
      </Modal>

      <BottomSheet
        title="حذف عضو"
        hideBottomSheet={() => setShowDeleteBottomSheet(false)}
        show={showDeleteBottomSheet}
      >
        <BottomSheetMessage>
          آیا می‌خواهید این عضو را از اعضای مرفوک حذف کنید؟
        </BottomSheetMessage>

        <Stack direction="row">
          <BottomSheetPrimaryButton onClick={() => deleteUser()}>
            بله
          </BottomSheetPrimaryButton>
          <BottomSheetSecondaryButton
            onClick={() => setShowDeleteBottomSheet(false)}
          >
            خیر
          </BottomSheetSecondaryButton>
        </Stack>
      </BottomSheet>
    </Grid>
  );
}

export default MembersSection;
