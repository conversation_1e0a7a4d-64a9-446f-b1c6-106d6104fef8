import { useState } from 'react';
import { MobileDatePicker, LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { faIR } from '@mui/x-date-pickers/locales';
import dayjs from 'dayjs';
import 'dayjs/locale/fa';

export default function JalaliDatePicker({
  label,
  inputName,
  defaultValue,
  setMinDateToday = false,
  size = 'small',
  onChange,
}) {
  const faLocale = {
    ...faIR.components.MuiLocalizationProvider.defaultProps.localeText,
    okButtonLabel: 'تایید',
  };

  const [selectedDate, setSelectedDate] = useState(defaultValue);

  const handleDateChange = newDate => {
    setSelectedDate(newDate);
    onChange(newDate);
  };

  const handleClear = () => {
    setSelectedDate(null);
  };

  const today = dayjs();

  return (
    <LocalizationProvider
      dateAdapter={AdapterDayjs}
      adapterLocale="fa"
      localeText={faLocale}
    >
      <MobileDatePicker
        label={label}
        value={selectedDate ? dayjs(selectedDate) : null}
        onChange={handleDateChange}
        format="YYYY-MM-DD"
        minDate={setMinDateToday ? today : null}
        inputFormat=""
        slotProps={{
          actionBar: {
            actions: ['clear', 'accept', 'cancel'],
            onClear: handleClear,
          },
          textField: {
            name: inputName,
            placeholder: '',
            size,
            fullWidth: true,
          },
        }}
        defaultValue={defaultValue ? dayjs(defaultValue) : null}
      />
    </LocalizationProvider>
  );
}
