import { ReactComponent as MicrophoneIcon } from './static/icons/bold/microphone.svg';
import { ReactComponent as MusicSquareIcon } from './static/icons/bold/music-square.svg';
import { ReactComponent as VideoPlayIcon } from './static/icons/bold/video-play.svg';
import { ReactComponent as VideoVerticalIcon } from './static/icons/bold/video-vertical.svg';
import { ReactComponent as VideoIcon } from './static/icons/bold/video.svg';

import { ReactComponent as ThreeDRotateIcon } from './static/icons/bold/3d-rotate.svg';
import { ReactComponent as DeviceMessageIcon } from './static/icons/bold/device-message.svg';
import { ReactComponent as Note2Icon } from './static/icons/bold/note-2.svg';

export const BASE_URL = process.env.REACT_APP_HOST;
export const WS_URL = process.env.REACT_APP_WS_URL;
export const APP_VERSION = '0.24.10';

export const drawerWidth = 240;

export const CONTENT_TYPES = [
  {
    name: 'موشن',
    icon: VideoVerticalIcon,
    value: 'M',
    color: '#FFA403',
    fileTypes: ['video'],
  },
  {
    name: 'ویدئو',
    icon: VideoIcon,
    value: 'V',
    color: '#63CB60',
    fileTypes: ['video'],
  },
  {
    name: 'نماهنگ',
    icon: MusicSquareIcon,
    value: 'MV',
    color: '#EF5524',
    fileTypes: ['video'],
  },
  {
    name: 'کلیپ',
    icon: VideoPlayIcon,
    value: 'C',
    color: '#0FD1BA',
    fileTypes: ['video'],
  },
  {
    name: 'استوری',
    icon: Note2Icon,
    value: 'S',
    color: '#E27856',
    fileTypes: ['image', 'video'],
  },
  {
    name: 'فتوتیتر',
    icon: DeviceMessageIcon,
    value: 'PT',
    color: '#D370C7',
    fileTypes: ['image'],
  },
  {
    name: 'تصویر سازی',
    icon: ThreeDRotateIcon,
    value: 'I',
    color: '#D72222',
    fileTypes: ['image'],
  },
  {
    name: 'پادکست',
    icon: MicrophoneIcon,
    value: 'P',
    color: '#0BABFE',
    fileTypes: ['audio'],
  },
];

export const GENDER = [
  {
    name: 'مرد',
    value: 'M',
  },
  {
    name: 'زن',
    value: 'F',
  },
];

export const MONTHS = [
  { value: 'FA', name: 'فروردین' },
  { value: 'OR', name: 'اردیبهشت' },
  { value: 'KH', name: 'خرداد' },
  { value: 'TI', name: 'تیر' },
  { value: 'MO', name: 'مرداد' },
  { value: 'SH', name: 'شهریور' },
  { value: 'ME', name: 'مهر' },
  { value: 'AB', name: 'آبان' },
  { value: 'AZ', name: 'آذر' },
  { value: 'DE', name: 'دی' },
  { value: 'BA', name: 'بهمن' },
  { value: 'ES', name: 'اسفند' },
];

export const PATHS = {
  enter_mobile: '/auth/enter-mobile',
  enter_otp: '/auth/validate-otp',
  login_please: '/auth/login-please',
  registration_form: '/auth/registration-form',
  home: '/',
  terms: '/terms',
  announcement: '/announcement',
  faq: '/faq',
  settings: '/settings',
  search: '/search',
  profile: '/profile/:userId',
  profileAnalytics: '/profile/:userId/analytics',

  createContent: '/content/create',
  content: '/content/:contentId',
  contentMarfook: '/content-marfook/:contentId',
  marfook: '/content-marfook',
  marfookAdmin: '/content-marfook-admin',
  contentReels: '/content/:contentId/reels',
  updateContent: '/content/:contentId/update',
  contentAnalytics: '/content/:contentId/analytics',
  contentEvaluate: '/content/:contentId/evaluate',
  contentEvaluateReport: '/content/:contentId/evaluate-report',
  forwardContent: '/content/:contentId/forward',
  contentInteractions: '/content/:contentId/interactions/:interactionType',

  chats: '/chats',
  chat: '/chat/:chatId',
  chatInfo: '/chat/:chatId/info',
  createChat: '/chat/create',
  createChatSelectUser: '/chat/select-user',
  createGroupSetDetail: '/chat/group/set-detail',
  createChannelSetDetail: '/chat/channel/set-detail',
  forwardChatsList: '/chat/forward/chats-list/:messageDeliveryToken',
  addParticipant: '/chat/:chatId/add-participant',
  meet: '/chat/:chatId/meet/:meetId',

  createContact: '/contact/create',

  admin: {
    homepage: '/admin-panel',

    announcementList: '/admin-panel/announcement',
    announcementCreate: '/admin-panel/announcement/create',
    announcementUpdate: '/admin-panel/announcement/:announcementId/update',
    announcementView: '/admin-panel/announcement/view/:announcementId',

    organizationList: '/admin-panel/organization',
    organizationUserList: '/admin-panel/organization/users',
    organizationGraph: '/admin-panel/organization/graph',
    organizationCreate: '/admin-panel/organization/create',
    organizationUpdate: '/admin-panel/organization/:organizationId/update',
    organizationView: '/admin-panel/organization/view/:organizationId',

    groupMsgList: '/admin-panel/group-msg',
    groupMsgCreate: '/admin-panel/group-msg/create',
    groupMsgView: '/admin-panel/group-msg/view/:groupMsgId',

    marfookUserList: '/admin-panel/marfook/users',
    marfookInstructions: '/admin-panel/marfook/instructions',
    marfookInstructionCreate: '/admin-panel/marfook/instructions/new',
    marfookInstructionView: '/admin-panel/marfook/instructions/:instructionId',
    marfookInstructionEdit:
      '/admin-panel/marfook/instructions/edit/:instructionId',
  },
};

export const CONTENT_CATEGORY = [
  { name: 'فرهنگی', value: 'C' },
  { name: 'سیاسی', value: 'P' },
  { name: 'انقلاب اسلامی', value: 'CP' },
  { name: 'مذهبی', value: 'K' },
  { name: 'اقتصادی', value: 'E' },
  { name: 'طنز و سرگرمی', value: 'CO' },
  { name: 'خانواده', value: 'F' },
  { name: 'اجتماعی', value: 'S' },
  { name: 'ورزشی', value: 'SP' },
  { name: 'دفاع مقدس', value: 'HD' },
  { name: 'روانشناسی', value: 'PS' },
  { name: 'فناوری', value: 'ST' },
  { name: 'آموزشی', value: 'EDUCATIONAL' },
  { name: 'شهدا', value: 'MARTYRS' },
  { name: 'مقاومت', value: 'RESISTANCE' },
  { name: 'مناسبت‌ها', value: 'OCCASION' },
  { name: 'خبری', value: 'NEWS' },
  { name: 'امنیتی', value: 'SECURITY' },
  { name: 'مهدویت', value: 'MAHDAVIAT' },
  { name: 'بین‌الملل', value: 'INTERNATIONAL' },
  { name: 'بدون دسته بندی', value: 'O' },
];

export const CONTENT_SHARE_TIME = [
  {
    name: 'همه',
    value: 'All',
    startHour: 0,
    endHour: 23,
  },
  // {
  //   name: '۷:۰۰ تا ۸:۰۰', value: '7', startHour: 7, endHour: 8,
  // },
  // {
  //   name: '۸:۰۰ تا ۹:۰۰', value: '8', startHour: 8, endHour: 9,
  // },
  // {
  //   name: '۹:۰۰ تا ۱۰:۰۰', value: '9', startHour: 9, endHour: 10,
  // },
  // {
  //   name: '۱۰:۰۰ تا ۱۱:۰۰', value: '10', startHour: 10, endHour: 11,
  // },
  // {
  //   name: '۱۱:۰۰ تا ۱۲:۰۰', value: '11', startHour: 11, endHour: 12,
  // },
  // {
  //   name: '۱۲:۰۰ تا ۱۳:۰۰', value: '12', startHour: 12, endHour: 13,
  // },
  // {
  //   name: '۱۳:۰۰ تا ۱۴:۰۰', value: '13', startHour: 13, endHour: 14,
  // },
  // {
  //   name: '۱۴:۰۰ تا ۱۵:۰۰', value: '14', startHour: 14, endHour: 15,
  // },
  // {
  //   name: '۱۵:۰۰ تا ۱۶:۰۰', value: '15', startHour: 15, endHour: 16,
  // },
  // {
  //   name: '۱۶:۰۰ تا ۱۷:۰۰', value: '16', startHour: 16, endHour: 17,
  // },
  // {
  //   name: '۱۷:۰۰ تا ۱۸:۰۰', value: '17', startHour: 17, endHour: 18,
  // },
  // {
  //   name: '۱۸:۰۰ تا ۱۹:۰۰', value: '18', startHour: 18, endHour: 19,
  // },
];

export const VIEW = 'view';
export const DOWNLOAD = 'download';
export const LIKE = 'like';
export const SHARE = 'share';
export const FAVORITE = 'favorite';
export const CONTENT_INTERACTIONS = [
  { name: 'view', value: VIEW },
  { name: 'download', value: DOWNLOAD },
  { name: 'like', value: LIKE },
  { name: 'share', value: SHARE },
  { name: 'favorite', value: FAVORITE },
];

export const CONTENT_ASPECT_RATIO = 3 / 4;
export const CONTENT_DIMENSION = {
  width: 300,
  height: 400,
};
export const PROFILE_DIMENTION = {
  width: 300,
  height: 300,
};

export const ORDERING = [
  {
    key: '-created_at',
    value: 'جدیدترین',
  },
  {
    key: 'created_at',
    value: 'قدیمی‌ترین',
  },
  {
    key: '-file_size',
    value: 'بیشترین حجم',
  },
  {
    key: 'file_size',
    value: 'کمترین حجم',
  },
  {
    key: '-likes_count',
    value: 'بیشترین لایک',
  },
  {
    key: '-downloads_count',
    value: 'بیشترین دانلود',
  },
  {
    key: '-favorites_count',
    value: 'بیشترین نشان شده',
  },
  {
    key: '-views_count',
    value: 'بیشترین بازدید',
  },
];

export const TILE_VIEW_MODE = 'tile';
export const LIST_VIEW_MODE = 'list';
export const CONTENT_VIEW_MODES = [
  { key: TILE_VIEW_MODE, value: 'کاشی' },
  { key: LIST_VIEW_MODE, value: 'لیست' },
];

export const CONTENT_STATUS = {
  WAITING: 'W',
  REJECTED: 'R',
  APPROVED: 'A',
  ARCHIVED: 'C',
};

export const SEARCH_LOCATIONS = [
  {
    name: 'هشتگ‌ها',
    value: 'labels',
  },
  {
    name: 'توضیحات',
    value: 'description',
  },
  {
    name: 'عنوان',
    value: 'title',
  },
  {
    name: 'نظرات',
    value: 'comment__comment',
  },
];

export const MESSAGE_TYPES = {
  GROUP_CREATE: 'gc',
  EVENT: 'e',
  TEXT: 't',
  FILE: 'f',
  IMAGE: 'i',
  VIDEO: 'v',
  AUDIO: 'a',
  CONTENT: 'c',
  CALL: 'ca',
};

export const TRANSLATED_MESSAGE_TYPES = {
  [MESSAGE_TYPES.GROUP_CREATE]: 'گفت‌وگو ایجاد شد',
  [MESSAGE_TYPES.FILE]: 'فایل',
  [MESSAGE_TYPES.IMAGE]: 'تصویر',
  [MESSAGE_TYPES.VIDEO]: 'ویدیو',
  [MESSAGE_TYPES.AUDIO]: 'صوت',
  [MESSAGE_TYPES.CONTENT]: 'محتوا',
  [MESSAGE_TYPES.EVENT]: 'رخداد',
};

export const CHAT_TYPES = {
  DIRECT: 'd',
  GROUP: 'g',
  CHANNEL: 'c',
};

export const MESSAGE_DELIVER_STATUS = {
  SENDING: 'sending',
  SENT: 'sent',
  RECEIVED: 'received',
  READ: 'read',
};

export const WS_MESSAGE_ACTIONS = {
  CHAT_BRIEF: 'chat_brief',
  MESSAGE: 'message',
  RECEIVED_MESSAGE: 'received_message',
  DELETE_MESSAGE: 'delete_message',
  READ_MESSAGE: 'read_message',
  GET_CHAT_MESSAGES: 'get_chat_messages',
};

export const SELECT_MESSAGE_REASONS = {
  EDIT: 'edit',
  REPLY: 'reply',
};

export const REPORT_ITEM_TYPE = {
  CONTENT: 'content',
  CHAT: 'chat',
  USER: 'user',
};

export const EVENT_MESSAGE_TYPES = {
  ADD_PARTICIPANT: 'add_participant',
  REMOVE_PARTICIPANT: 'remove_participant',
  ADD_ADMIN: 'add_admin',
  REMOVE_ADMIN: 'remove_admin',
  BLOCK_USER: 'block_user',
  UNBLOCK_USER: 'unblock_user',
};

export const INTERACTION_RESPONSE_TYPES = {
  LIKE: 'like',
  FOLLOW: 'follow',
  COMMENT: 'comment',
  NOTE: 'note',
};
