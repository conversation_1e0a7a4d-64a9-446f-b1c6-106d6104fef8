import { Box, Typography } from '@mui/material';
import { useQuery } from 'react-query';
import { getMarfookUserList } from 'apis/marfook';
import { PATHS } from '../../../constants';
import MembersSection from './components/MembersSection/MembersSection';
import LoadingPage from '../../../components/LoadingPage/LoadingPage';

export default function MarfookUserList() {
  const { isLoading, data } = useQuery([PATHS.admin.marfookUserList], () =>
    getMarfookUserList(),
  );
  const userList = isLoading ? {} : data?.data;

  if (isLoading) {
    return <LoadingPage />;
  }

  return (
    <>
      <Box sx={{ width: '100%', marginBottom: '20px', textAlign: 'center' }}>
        <Typography
          variant="h4"
          sx={{ mt: 3, fontSize: 17, fontWeight: 'bold' }}
        >
          لیست اعضای مرفوک
        </Typography>
      </Box>

      <MembersSection list={userList} />
    </>
  );
}
